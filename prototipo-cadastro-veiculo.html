<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Protótipo - Cadastrar Veículo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            margin: 0;
            padding: 20px;
        }
        
        .modal {
            background-color: white;
            max-width: 500px;
            margin: 0 auto;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .modal-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #6c757d;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        .required {
            color: #dc3545;
        }
        
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #80bdff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }
        
        .select-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            background-color: white;
            box-sizing: border-box;
        }
        
        .collapsible {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            margin: 20px 0;
        }
        
        .collapsible-header {
            padding: 12px 15px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f8f9fa;
        }
        
        .collapsible-header:hover {
            background-color: #e9ecef;
        }
        
        .collapsible-content {
            padding: 15px;
            border-top: 1px solid #dee2e6;
            display: none;
        }
        
        .collapsible-content.show {
            display: block;
        }
        
        .chevron {
            transition: transform 0.3s ease;
        }
        
        .chevron.rotate {
            transform: rotate(180deg);
        }
        
        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: 1px solid;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-secondary {
            background-color: white;
            border-color: #6c757d;
            color: #6c757d;
        }
        
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
        }
        
        .conditional-field {
            display: none;
        }
        
        .conditional-field.show {
            display: block;
        }
        
        .detran-label {
            font-size: 12px;
            color: #6c757d;
            font-weight: normal;
        }
    </style>
</head>
<body>
    <div class="modal">
        <div class="modal-header">
            <h2 class="modal-title">Cadastrar veículo</h2>
            <button class="close-btn">&times;</button>
        </div>
        
        <div class="modal-body">
            <!-- Campos principais (fora do accordion) -->
            <div class="form-group">
                <label class="form-label">Placa<span class="required">*</span></label>
                <input type="text" class="form-control" placeholder="">
            </div>
            
            <div class="form-group">
                <label class="form-label">Renavam<span class="required">*</span></label>
                <input type="text" class="form-control" placeholder="00000000-0">
            </div>
            
            <div class="form-group">
                <label class="form-label">UF<span class="required">*</span></label>
                <select class="select-control" id="uf-select" onchange="toggleConditionalFields()">
                    <option value="">Selecione...</option>
                    <option value="AC">AC</option>
                    <option value="AL">AL</option>
                    <option value="AP">AP</option>
                    <option value="AM">AM</option>
                    <option value="BA">BA</option>
                    <option value="CE">CE</option>
                    <option value="DF">DF</option>
                    <option value="ES">ES</option>
                    <option value="GO">GO</option>
                    <option value="MA">MA</option>
                    <option value="MT">MT</option>
                    <option value="MS">MS</option>
                    <option value="MG">MG</option>
                    <option value="PA">PA</option>
                    <option value="PB">PB</option>
                    <option value="PR">PR</option>
                    <option value="PE">PE</option>
                    <option value="PI">PI</option>
                    <option value="RJ">RJ</option>
                    <option value="RN">RN</option>
                    <option value="RS">RS</option>
                    <option value="RO">RO</option>
                    <option value="RR">RR</option>
                    <option value="SC">SC</option>
                    <option value="SP">SP</option>
                    <option value="SE">SE</option>
                    <option value="TO">TO</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">Empresa responsável<span class="required">*</span></label>
                <select class="select-control">
                    <option value="">Selecione...</option>
                    <option value="empresa1">Empresa 1</option>
                    <option value="empresa2">Empresa 2</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">Cronotacógrafo Habilitado<span class="required">*</span></label>
                <select class="select-control">
                    <option value="">Selecione...</option>
                    <option value="sim">Sim</option>
                    <option value="nao">Não</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">SNE habilitado<span class="required">*</span></label>
                <select class="select-control">
                    <option value="">Selecione...</option>
                    <option value="sim">Sim</option>
                    <option value="nao">Não</option>
                </select>
            </div>

            <!-- Campos sempre visíveis, asterisco condicional -->
            <div class="form-group">
                <label class="form-label">
                    Chassi<span class="required" id="chassi-asterisk" style="display: none;">*</span>
                </label>
                <input type="text" class="form-control">
            </div>

            <div class="form-group">
                <label class="form-label">
                    Código de segurança(Detran/SE)<span class="required" id="codigo-asterisk" style="display: none;">*</span>
                </label>
                <input type="text" class="form-control">
            </div>
            
            <!-- Accordion para dados adicionais -->
            <div class="collapsible">
                <div class="collapsible-header" onclick="toggleCollapsible()">
                    <span>Dados adicionais (opcional)</span>
                    <span class="chevron" id="chevron">▼</span>
                </div>
                <div class="collapsible-content" id="collapsible-content">
                    <div class="form-group">
                        <label class="form-label">Motorista</label>
                        <input type="text" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Marca</label>
                        <input type="text" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Modelo</label>
                        <input type="text" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Tipo</label>
                        <input type="text" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Utilização</label>
                        <input type="text" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Código da frota</label>
                        <input type="text" class="form-control">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="modal-footer">
            <button class="btn btn-secondary">Cancelar</button>
            <button class="btn btn-primary">Salvar</button>
        </div>
    </div>

    <script>
        function toggleCollapsible() {
            const content = document.getElementById('collapsible-content');
            const chevron = document.getElementById('chevron');
            
            content.classList.toggle('show');
            chevron.classList.toggle('rotate');
        }
        
        function toggleConditionalFields() {
            const ufSelect = document.getElementById('uf-select');
            const selectedUF = ufSelect.value;

            const chassiAsterisk = document.getElementById('chassi-asterisk');
            const codigoAsterisk = document.getElementById('codigo-asterisk');

            // Esconder asteriscos por padrão
            chassiAsterisk.style.display = 'none';
            codigoAsterisk.style.display = 'none';

            // Mostrar asteriscos baseado na UF selecionada
            if (selectedUF === 'MG') {
                chassiAsterisk.style.display = 'inline';
            } else if (selectedUF === 'SE') {
                codigoAsterisk.style.display = 'inline';
            }
        }
    </script>
</body>
</html>
